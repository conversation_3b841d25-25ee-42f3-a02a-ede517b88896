#!/usr/bin/env python3
"""
Test script to verify the fourth stage (SOLVE) implementation
"""

from self_discover import SelfDiscover

# Simple test task
test_task = """
Solve this math problem step by step:
A store sells apples for $2 per pound and oranges for $3 per pound. 
If someone buys 4 pounds of apples and 2 pounds of oranges, how much do they spend in total?
"""

def test_solve_stage():
    """Test that the SOLVE stage is properly implemented"""
    print("Testing SELF-DISCOVER with SOLVE stage...")
    print("=" * 50)
    
    # Create SelfDiscover instance
    result = SelfDiscover(task=test_task)
    
    # Check that SOLVE is in the actions
    assert "SOLVE" in result.actions, "SOLVE stage not found in actions"
    print("✓ SOLVE stage found in actions")
    
    # Check that all 4 stages are present
    expected_actions = ["SELECT", "ADAPT", "IMPLEMENT", "SOLVE"]
    assert result.actions == expected_actions, f"Expected {expected_actions}, got {result.actions}"
    print("✓ All 4 stages present in correct order")
    
    print("\nTest completed successfully!")
    print("The fourth stage (SOLVE) has been properly implemented.")
    print("\nTo run the full process with an API key, use:")
    print("python self_discover.py")
    print("or run the Streamlit app with:")
    print("streamlit run app.py")

if __name__ == "__main__":
    test_solve_stage()
